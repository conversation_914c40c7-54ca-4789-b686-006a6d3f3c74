import os
import shutil
import subprocess
import sys

# إعداد المسارات
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
# استخدام Python من البيئة الافتراضية إذا كانت متاحة
VENV_PYTHON = os.path.join(BASE_DIR, "venv", "Scripts", "python.exe")
if os.path.exists(VENV_PYTHON):
    PYINSTALLER_CMD = f'"{VENV_PYTHON}" -m PyInstaller'
else:
    PYINSTALLER_CMD = f"{sys.executable} -m PyInstaller"
INNO_SETUP_PATH = r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
DIST_DIR = os.path.join(BASE_DIR, "dist")
FONTS_DIR = os.path.join(BASE_DIR, "fonts")
CONFIG_FILE = os.path.join(BASE_DIR, "config.json")
EXE_NAME = "gui.exe"
PY_FILE = os.path.join(BASE_DIR, "gui.py")
ISS_FILE = os.path.join(BASE_DIR, "installer.iss")

# 1. تجميع البرنامج إلى ملف تنفيذي
print("🔄 تجميع البرنامج إلى ملف تنفيذي...")
if os.path.exists(DIST_DIR):
    shutil.rmtree(DIST_DIR)

# استخدام ملف spec مخصص
SPEC_FILE = os.path.join(BASE_DIR, "gui.spec")
cmd = f'{PYINSTALLER_CMD} --noconfirm "{SPEC_FILE}"'
res = subprocess.run(cmd, shell=True)
if res.returncode != 0 or not os.path.exists(os.path.join(DIST_DIR, EXE_NAME)):
    print("❌ فشل في تجميع البرنامج!")
    sys.exit(1)

# 2. تجهيز سكربت Inno Setup
print("🔄 تجهيز سكربت Inno Setup...")
iss_content = f'''
[Setup]
AppName=LaraLab Print Agent
AppVersion=2.0
DefaultDirName={{pf}}\\LaraLabPrintAgent
DefaultGroupName=LaraLab Print Agent
OutputDir=.
OutputBaseFilename=LaraLabPrintAgentSetup
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"

[Files]
Source: "dist\\gui.exe"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "config.json"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "fonts\\*"; DestDir: "{{app}}\\fonts"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\LaraLab Print Agent"; Filename: "{{app}}\\gui.exe"
Name: "{{userdesktop}}\\LaraLab Print Agent"; Filename: "{{app}}\\gui.exe"

[Run]
Filename: "{{app}}\\gui.exe"; Description: "تشغيل البرنامج الآن"; Flags: nowait postinstall skipifsilent

[Registry]
Root: HKCU; Subkey: "Software\\Microsoft\\Windows\\CurrentVersion\\Run"; ValueType: string; ValueName: "LaraLabPrintAgent"; ValueData: """{{app}}\\gui.exe"""
'''
with open(ISS_FILE, "w", encoding="utf-8") as f:
    f.write(iss_content)

# 3. تشغيل Inno Setup لإنشاء ملف التثبيت
print("🔄 إنشاء ملف التثبيت النهائي...")
res = subprocess.run([INNO_SETUP_PATH, ISS_FILE])
if res.returncode != 0:
    print("❌ فشل في إنشاء ملف التثبيت!")
    sys.exit(2)

# 4. تنظيف الملفات المؤقتة
print("🧹 تنظيف الملفات المؤقتة...")
for folder in ["build", "__pycache__"]:
    path = os.path.join(BASE_DIR, folder)
    if os.path.exists(path):
        shutil.rmtree(path)
for file in [ISS_FILE]:
    path = os.path.join(BASE_DIR, file)
    if os.path.exists(path):
        os.remove(path)

print("\n✅ تم تجهيز ملف التثبيت بنجاح! ستجده باسم LaraLabPrintAgentSetup.exe في هذا المجلد.") 