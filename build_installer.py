import os
import shutil
import subprocess
import sys

# إعداد المسارات
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
# استخدام Python العام مباشرة
PYINSTALLER_CMD = f"{sys.executable} -m PyInstaller"
INNO_SETUP_PATH = r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
DIST_DIR = os.path.join(BASE_DIR, "dist")
FONTS_DIR = os.path.join(BASE_DIR, "fonts")
CONFIG_FILE = os.path.join(BASE_DIR, "config.json")
EXE_NAME = "gui.exe"
PY_FILE = os.path.join(BASE_DIR, "gui.py")
ISS_FILE = os.path.join(BASE_DIR, "installer.iss")

# 1. تجميع البرنامج إلى ملف تنفيذي
print("🔄 تجميع البرنامج إلى ملف تنفيذي...")
if os.path.exists(DIST_DIR):
    shutil.rmtree(DIST_DIR)

# استخدام PyInstaller مباشرة مع جميع الخيارات المطلوبة
hidden_imports = [
    "--hidden-import=flask",
    "--hidden-import=flask.app",
    "--hidden-import=flask.blueprints",
    "--hidden-import=flask.ctx",
    "--hidden-import=flask.globals",
    "--hidden-import=flask.helpers",
    "--hidden-import=flask.json",
    "--hidden-import=flask.logging",
    "--hidden-import=flask.sessions",
    "--hidden-import=flask.signals",
    "--hidden-import=flask.templating",
    "--hidden-import=flask.testing",
    "--hidden-import=flask.wrappers",
    "--hidden-import=flask_cors",
    "--hidden-import=werkzeug",
    "--hidden-import=werkzeug.serving",
    "--hidden-import=werkzeug.utils",
    "--hidden-import=werkzeug.wrappers",
    "--hidden-import=werkzeug.exceptions",
    "--hidden-import=werkzeug.routing",
    "--hidden-import=werkzeug.http",
    "--hidden-import=werkzeug.urls",
    "--hidden-import=werkzeug.datastructures",
    "--hidden-import=werkzeug.security",
    "--hidden-import=jinja2",
    "--hidden-import=jinja2.environment",
    "--hidden-import=jinja2.loaders",
    "--hidden-import=jinja2.runtime",
    "--hidden-import=jinja2.compiler",
    "--hidden-import=jinja2.filters",
    "--hidden-import=jinja2.utils",
    "--hidden-import=click",
    "--hidden-import=click.core",
    "--hidden-import=click.decorators",
    "--hidden-import=click.exceptions",
    "--hidden-import=click.formatting",
    "--hidden-import=click.parser",
    "--hidden-import=click.types",
    "--hidden-import=click.utils",
    "--hidden-import=itsdangerous",
    "--hidden-import=markupsafe",
    "--hidden-import=blinker",
    "--hidden-import=server",
    "--hidden-import=printer_core",
    "--hidden-import=label_design"
]

collect_all = [
    "--collect-all=flask",
    "--collect-all=flask_cors",
    "--collect-all=werkzeug",
    "--collect-all=jinja2"
]

cmd = f'{PYINSTALLER_CMD} --noconfirm --onefile --windowed --add-data "fonts;fonts" --add-data "config.json;." {" ".join(hidden_imports)} {" ".join(collect_all)} gui.py'
res = subprocess.run(cmd, shell=True)
if res.returncode != 0 or not os.path.exists(os.path.join(DIST_DIR, EXE_NAME)):
    print("❌ فشل في تجميع البرنامج!")
    sys.exit(1)

# 2. تجهيز سكربت Inno Setup
print("🔄 تجهيز سكربت Inno Setup...")
iss_content = f'''
[Setup]
AppName=LaraLab Print Agent
AppVersion=2.0
DefaultDirName={{pf}}\\LaraLabPrintAgent
DefaultGroupName=LaraLab Print Agent
OutputDir=.
OutputBaseFilename=LaraLabPrintAgentSetup
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"

[Files]
Source: "dist\\gui.exe"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "config.json"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "fonts\\*"; DestDir: "{{app}}\\fonts"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\LaraLab Print Agent"; Filename: "{{app}}\\gui.exe"
Name: "{{userdesktop}}\\LaraLab Print Agent"; Filename: "{{app}}\\gui.exe"

[Run]
Filename: "{{app}}\\gui.exe"; Description: "تشغيل البرنامج الآن"; Flags: nowait postinstall skipifsilent

[Registry]
Root: HKCU; Subkey: "Software\\Microsoft\\Windows\\CurrentVersion\\Run"; ValueType: string; ValueName: "LaraLabPrintAgent"; ValueData: """{{app}}\\gui.exe"""
'''
with open(ISS_FILE, "w", encoding="utf-8") as f:
    f.write(iss_content)

# 3. تشغيل Inno Setup لإنشاء ملف التثبيت
print("🔄 إنشاء ملف التثبيت النهائي...")
res = subprocess.run([INNO_SETUP_PATH, ISS_FILE])
if res.returncode != 0:
    print("❌ فشل في إنشاء ملف التثبيت!")
    sys.exit(2)

# 4. تنظيف الملفات المؤقتة
print("🧹 تنظيف الملفات المؤقتة...")
for folder in ["build", "__pycache__"]:
    path = os.path.join(BASE_DIR, folder)
    if os.path.exists(path):
        shutil.rmtree(path)
for file in [ISS_FILE]:
    path = os.path.join(BASE_DIR, file)
    if os.path.exists(path):
        os.remove(path)

print("\n✅ تم تجهيز ملف التثبيت بنجاح! ستجده باسم LaraLabPrintAgentSetup.exe في هذا المجلد.") 