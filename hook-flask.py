# PyInstaller hook for Flask
from PyInstaller.utils.hooks import collect_all, collect_submodules

# Collect all Flask modules
datas, binaries, hiddenimports = collect_all('flask')

# Add additional hidden imports for Flask
hiddenimports += [
    'flask.app',
    'flask.blueprints',
    'flask.ctx',
    'flask.globals',
    'flask.helpers',
    'flask.json',
    'flask.logging',
    'flask.sessions',
    'flask.signals',
    'flask.templating',
    'flask.testing',
    'flask.wrappers',
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'werkzeug.wrappers',
    'werkzeug.exceptions',
    'werkzeug.routing',
    'werkzeug.http',
    'werkzeug.urls',
    'werkzeug.datastructures',
    'werkzeug.security',
    'jinja2',
    'jinja2.environment',
    'jinja2.loaders',
    'jinja2.runtime',
    'jinja2.compiler',
    'jinja2.filters',
    'jinja2.utils',
    'click',
    'click.core',
    'click.decorators',
    'click.exceptions',
    'click.formatting',
    'click.options',
    'click.parser',
    'click.types',
    'click.utils',
    'itsdangerous',
    'itsdangerous.encoding',
    'itsdangerous.exc',
    'itsdangerous.serializer',
    'itsdangerous.signer',
    'itsdangerous.timed',
    'itsdangerous.url_safe',
    'markupsafe',
    'blinker',
    'blinker.base',
    'blinker.signals'
]

# Collect submodules for key packages
hiddenimports += collect_submodules('flask')
hiddenimports += collect_submodules('werkzeug')
hiddenimports += collect_submodules('jinja2')
hiddenimports += collect_submodules('click')
hiddenimports += collect_submodules('itsdangerous')
hiddenimports += collect_submodules('markupsafe')
hiddenimports += collect_submodules('blinker')
