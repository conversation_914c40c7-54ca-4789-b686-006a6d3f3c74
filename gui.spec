# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('fonts', 'fonts'), ('config.json', '.')]
binaries = []
hiddenimports = ['flask', 'flask.app', 'flask.blueprints', 'flask.ctx', 'flask.globals', 'flask.helpers', 'flask.json', 'flask.logging', 'flask.sessions', 'flask.signals', 'flask.templating', 'flask.testing', 'flask.wrappers', 'flask_cors', 'werkzeug', 'werkzeug.serving', 'werkzeug.utils', 'werkzeug.wrappers', 'werkzeug.exceptions', 'werkzeug.routing', 'werkzeug.http', 'werkzeug.urls', 'werkzeug.datastructures', 'werkzeug.security', 'jinja2', 'jinja2.environment', 'jinja2.loaders', 'jinja2.runtime', 'jinja2.compiler', 'jinja2.filters', 'jinja2.utils', 'click', 'click.core', 'click.decorators', 'click.exceptions', 'click.formatting', 'click.parser', 'click.types', 'click.utils', 'itsdangerous', 'markupsafe', 'blinker', 'server', 'printer_core', 'label_design']
tmp_ret = collect_all('flask')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('flask_cors')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('werkzeug')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('jinja2')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['gui.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='gui',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
