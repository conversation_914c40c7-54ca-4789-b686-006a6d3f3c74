# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

block_cipher = None

# Collect all Flask and related packages
flask_datas, flask_binaries, flask_hiddenimports = collect_all('flask')
werkzeug_datas, werkzeug_binaries, werkzeug_hiddenimports = collect_all('werkzeug')
jinja2_datas, jinja2_binaries, jinja2_hiddenimports = collect_all('jinja2')
click_datas, click_binaries, click_hiddenimports = collect_all('click')
itsdangerous_datas, itsdangerous_binaries, itsdangerous_hiddenimports = collect_all('itsdangerous')
markupsafe_datas, markupsafe_binaries, markupsafe_hiddenimports = collect_all('markupsafe')
blinker_datas, blinker_binaries, blinker_hiddenimports = collect_all('blinker')
flask_cors_datas, flask_cors_binaries, flask_cors_hiddenimports = collect_all('flask_cors')

a = Analysis(
    ['gui.py'],
    pathex=[],
    binaries=flask_binaries + werkzeug_binaries + jinja2_binaries + click_binaries +
             itsdangerous_binaries + markupsafe_binaries + blinker_binaries + flask_cors_binaries,
    datas=[
        ('fonts', 'fonts'),
        ('config.json', '.'),
    ] + flask_datas + werkzeug_datas + jinja2_datas + click_datas +
        itsdangerous_datas + markupsafe_datas + blinker_datas + flask_cors_datas,
    hiddenimports=[
        'flask',
        'flask.app',
        'flask.blueprints',
        'flask.ctx',
        'flask.globals',
        'flask.helpers',
        'flask.json',
        'flask.logging',
        'flask.sessions',
        'flask.signals',
        'flask.templating',
        'flask.testing',
        'flask.wrappers',
        'flask_cors',
        'flask_cors.core',
        'flask_cors.decorator',
        'flask_cors.extension',
        'werkzeug',
        'werkzeug.serving',
        'werkzeug.utils',
        'werkzeug.wrappers',
        'werkzeug.exceptions',
        'werkzeug.routing',
        'werkzeug.http',
        'werkzeug.urls',
        'werkzeug.datastructures',
        'werkzeug.security',
        'jinja2',
        'jinja2.environment',
        'jinja2.loaders',
        'jinja2.runtime',
        'jinja2.compiler',
        'jinja2.filters',
        'jinja2.utils',
        'click',
        'click.core',
        'click.decorators',
        'click.exceptions',
        'click.formatting',
        'click.options',
        'click.parser',
        'click.types',
        'click.utils',
        'itsdangerous',
        'itsdangerous.encoding',
        'itsdangerous.exc',
        'itsdangerous.serializer',
        'itsdangerous.signer',
        'itsdangerous.timed',
        'itsdangerous.url_safe',
        'markupsafe',
        'blinker',
        'blinker.base',
        'blinker.signals',
        'arabic_reshaper',
        'bidi',
        'bidi.algorithm',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'barcode',
        'barcode.writer',
        'win32print',
        'win32api',
        'win32gui',
        'win32con',
        'server',
        'printer_core',
        'label_design'
    ],
    hookspath=['.'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='gui',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
